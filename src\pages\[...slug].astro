---
import MainLayout from "../layouts/MainLayout.astro";
import {
  getCategories,
  getFilteredProducts,
  getProductBySlug,
} from "../db/database";
import ProductsPage from "../components/homepage/ProductsPage";
import ProductSkeleton from "../components/common/ProductSkeleton";

// For SSR pages we don't use getStaticPaths()
export const prerender = false;

// Parse the slug to extract filter parameters with improved readability
function parseSlug(slug: string) {
  if (!slug) return {};

  // Split the slug and remove empty segments
  const segments = slug.split("/").filter(Boolean);

  const filters: { [key: string]: any } = {};

  // Check if this is a product detail page (last segment doesn't match our filter patterns)
  if (
    segments.length > 0 &&
    ![
      "category",
      "search",
      "filter",
      "price-range",
      "order-by",
      "page",
    ].includes(segments[0])
  ) {
    // This might be a direct product URL like "/chicken-sandwich"
    filters.productSlug = segments[0];
    return filters;
  }

  // Process segments in pairs (key/value) for category browsing pages
  for (let i = 0; i < segments.length; i += 2) {
    const key = segments[i];
    const value = segments[i + 1];

    if (key && value) {
      switch (key) {
        case "category":
          filters.category = value;
          break;
        case "search":
          filters.search = value;
          break;
        case "order-by": // More readable than 'sort'
          filters.sort = value;
          break;
        case "price-range": // More readable than 'price'
          // Handle price range format: min-max, above-X, under-X
          if (value.includes("-")) {
            const [min, max] = value.split("-");
            if (min && min !== "under") filters.minPrice = parseInt(min);
            if (max && max !== "above") filters.maxPrice = parseInt(max);
          } else if (value.startsWith("above-")) {
            filters.minPrice = parseInt(value.replace("above-", ""));
          } else if (value.startsWith("under-")) {
            filters.maxPrice = parseInt(value.replace("under-", ""));
          }
          break;
        case "filter": // More readable than 'tags'
          filters.tags = value.split(",");
          break;
        case "page":
          filters.page = parseInt(value);
          break;
      }
    }
  }

  return filters;
}

// Define allowed paths for our dynamic routing
const validPaths = [
  "category",
  "search",
  "filter",
  "price-range",
  "order-by",
  "page",
];

// Extract filters from both slug and query parameters
const { slug } = Astro.params;
const url = Astro.url;
const pathname = url.pathname;

// Get filters from slug path parameters
const pathFilters = parseSlug(slug as string);

// Check if this is a product detail page
if (pathFilters.productSlug) {
  // Check if product exists before redirecting
  try {
    const productExists = await getProductBySlug(Astro.locals.runtime.env, pathFilters.productSlug);

    if (productExists) {
      // Product exists, redirect to the proper product page
      return Astro.redirect(`/product/${pathFilters.productSlug}`);
    } else {
      // Product doesn't exist, redirect to 404 page
      console.warn(`No product found with slug: ${pathFilters.productSlug}`);
      return Astro.redirect('/404');
    }
  } catch (error) {
    console.error(`Error checking product existence: ${error}`);
    return Astro.redirect('/404');
  }
}

// Get URL search parameters (for backward compatibility)
const urlCategory =
  url.searchParams.get("category") || pathFilters.category || "all";
const urlSort = url.searchParams.get("sort") || pathFilters.sort || "newest";
const urlSearch = url.searchParams.get("search") || pathFilters.search || "";
const urlMaxPrice = url.searchParams.has("maxPrice")
  ? parseInt(url.searchParams.get("maxPrice") || "0")
  : pathFilters.maxPrice || 10000;
const urlMinPrice = url.searchParams.has("minPrice")
  ? parseInt(url.searchParams.get("minPrice") || "0")
  : pathFilters.minPrice || 0;
const urlTags = url.searchParams.get("tags")
  ? (url.searchParams.get("tags") || "").split(",")
  : pathFilters.tags || [];
const urlPage = url.searchParams.has("page")
  ? parseInt(url.searchParams.get("page") || "1")
  : pathFilters.page || 1;

// Define fallback categories in case database is unavailable
const fallbackCategories = [
  { id: 1, name: "Fiction", icon: "📚", color: "#FF9F80" },
  { id: 2, name: "Non-Fiction", icon: "📖", color: "#E0C094" },
  { id: 3, name: "Educational", icon: "🎓", color: "#C79F7A" },
  { id: 4, name: "Children's Books", icon: "🧸", color: "#8ECAE6" },
  { id: 5, name: "Reference", icon: "📋", color: "#D9BF77" },
];

// Safely fetch categories from database with fallback
let dbCategories = [];
try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    dbCategories = await getCategories(Astro.locals.runtime.env);
  } else {
    console.warn(
      "Runtime environment not available, using fallback categories"
    );
    dbCategories = fallbackCategories;
  }
} catch (error) {
  console.error("Error fetching categories:", error);
  dbCategories = fallbackCategories;
}

// Create a combined list with "All Products" at the beginning
const categories = [
  { id: "all", name: "All Products" },
  ...dbCategories.map((cat) => ({ id: cat.id.toString(), name: cat.name })),
];

// Fetch initial products based on URL parameters
let initialProducts = [];
let totalCount = 0;
let hasMore = false;

try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    const page = urlPage;
    const limit = 12;

    // Construct filter object
    const filters = {
      category: urlCategory,
      sort: urlSort,
      search: urlSearch,
      minPrice: urlMinPrice,
      maxPrice: urlMaxPrice,
      tags: urlTags,
    };

    const result = await getFilteredProducts(Astro.locals.runtime.env, {
      page,
      limit,
      ...filters,
    });

    initialProducts = result.products || [];
    totalCount = result.totalCount || 0;
    hasMore = result.hasMore || false;
  }
} catch (error) {
  console.error("Error fetching initial products:", error);
}

// Generate SEO-friendly canonical paths for each filter type
function generatePathUrl(params = {} as any) {
  const baseUrl = new URL(url.origin);
  const pathSegments = [];

  // Add segments based on provided filters
  if (params.category && params.category !== "all") {
    pathSegments.push("category", params.category);
  }

  if (params.search) {
    pathSegments.push("search", params.search);
  }

  if (params.sort && params.sort !== "newest") {
    pathSegments.push("order-by", params.sort);
  }

  if (params.tags && params.tags.length > 0) {
    pathSegments.push("filter", params.tags.join(","));
  }

  if (
    (params.minPrice && params.minPrice > 0) ||
    (params.maxPrice && params.maxPrice < 10000)
  ) {
    const minVal = params.minPrice || 0;
    const maxVal = params.maxPrice || 10000;

    if (minVal > 0 && maxVal < 10000) {
      pathSegments.push("price-range", `${minVal}-${maxVal}`);
    } else if (minVal > 0) {
      pathSegments.push("price-range", `above-${minVal}`);
    } else if (maxVal < 10000) {
      pathSegments.push("price-range", `under-${maxVal}`);
    }
  }

  if (params.page && params.page > 1) {
    pathSegments.push("page", params.page.toString());
  }

  // Construct the path
  if (pathSegments.length > 0) {
    baseUrl.pathname = `/${pathSegments.join("/")}`;
  } else {
    baseUrl.pathname = "/"; // Default to /menu for the main products page
  }

  return baseUrl.toString();
}

// Generate the canonical URL for current filters
const canonicalUrl = generatePathUrl({
  category: urlCategory,
  sort: urlSort,
  search: urlSearch,
  minPrice: urlMinPrice,
  maxPrice: urlMaxPrice,
  tags: urlTags,
  page: urlPage,
});

// Build breadcrumb structure for SEO
const breadcrumbs = [
  { name: "Home", url: "/" },
  { name: "Menu", url: "/menu" },
];

// Add category breadcrumb if applicable
if (urlCategory && urlCategory !== "all") {
  const categoryName =
    categories.find((c) => c.id === urlCategory)?.name || urlCategory;
  breadcrumbs.push({
    name: categoryName,
    url: generatePathUrl({ category: urlCategory }),
  });
}

// Add search result breadcrumb if applicable
if (urlSearch) {
  breadcrumbs.push({ name: `Search: ${urlSearch}`, url: canonicalUrl });
}

// Prepare initial data to pass to the React component
const initialData = {
  categories,
  products: initialProducts,
  totalCount,
  hasMore,
  currentUrl: url.toString(),
  canonicalUrl,
  baseUrl: `${url.protocol}//${url.host}`,
  pathMode: true, // Use path-based routing instead of query parameters
  activeFilters: {
    category: urlCategory,
    tags: urlTags,
    minPrice: urlMinPrice,
    maxPrice: urlMaxPrice,
  },
  selectedSort: urlSort,
  searchQuery: urlSearch,
  currentPage: urlPage,
  breadcrumbs,
};

// Determine page title based on filters
let pageTitle = " Sreekar Publishers";
let pageDescription = "Browse our extensive collection of books across all genres.";

if (urlSearch) {
  pageTitle = `Search Results: ${urlSearch}`;
  pageDescription = `Find the best books matching "${urlSearch}" in our catalog.`;
} else if (urlCategory && urlCategory !== "all") {
  const categoryName =
    categories.find((c) => c.id === urlCategory)?.name || urlCategory;
  pageTitle = `${categoryName}`;
  pageDescription = `Explore our selection of ${categoryName.toLowerCase()} books.`;
}

// Add price range to description if applicable
if (urlMinPrice > 0 || urlMaxPrice < 10000) {
  let priceDesc = "";
  if (urlMinPrice > 0 && urlMaxPrice < 10000) {
    priceDesc = ` priced between $${urlMinPrice} and $${urlMaxPrice}`;
  } else if (urlMinPrice > 0) {
    priceDesc = ` priced above $${urlMinPrice}`;
  } else if (urlMaxPrice < 10000) {
    priceDesc = ` priced under $${urlMaxPrice}`;
  }
  pageDescription += priceDesc;
}
---

<MainLayout
  title={`${pageTitle} - Sreekar Publishers`}
  description={pageDescription}
  headerTitle={pageTitle}
  showHeader={true}
  showBackButton={false}
  canonicalURL={canonicalUrl}
  schema={{
    title: `${pageTitle} - Sreekar Publishers`,
    description: pageDescription,
    url: canonicalUrl,
    image: `${url.origin}/images/sreekar-publishers-social-image.png`,
    type: "website",
  }}
>
  <div
    class="breadcrumbs-container py-2 px-4 text-sm text-gray-600"
    aria-label="Breadcrumb"
  >
    <ol class="list-none p-0 inline-flex">
      {
        breadcrumbs.map((crumb, index) => (
          <li class="flex items-center">
            {index > 0 && <span class="mx-2">/</span>}
            {index === breadcrumbs.length - 1 ? (
              <span aria-current="page">{crumb.name}</span>
            ) : (
              <a href={crumb.url} class="hover:underline">
                {crumb.name}
              </a>
            )}
          </li>
        ))
      }
    </ol>
  </div>

  <ProductsPage client:load initialData={initialData as any}>
    <ProductSkeleton slot="fallback" count={12} />
  </ProductsPage>
</MainLayout>

<style is:inline>
  /* Improved scrollbar hiding */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Enhanced range slider styling */
  input[type="range"] {
    -webkit-appearance: none;
    height: 6px;
    background: #e5e7eb;
    border-radius: 5px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 22px;
    width: 22px;
    border-radius: 50%;
    background: #5466f7;
    box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
    cursor: pointer;
    border: 3px solid white;
  }

  input[type="range"]::-moz-range-thumb {
    height: 22px;
    width: 22px;
    border-radius: 50%;
    background: #5466f7;
    box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
    cursor: pointer;
    border: 3px solid white;
  }

  input[type="range"]::-webkit-slider-runnable-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
  }

  /* Product animations */

  @keyframes fade {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Product card transition animations */
  .product-skeleton,
  .product-card {
     contain: layout;
  }

  /* Breadcrumb styling */
  .breadcrumbs-container {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }
</style>
