import React, { useState, useEffect } from 'react';

export default function AdminSidebar({ isMobileMenuOpen, closeMobileMenu }) {
  // Track active menu item based on current URL
  const [activeItem, setActiveItem] = useState('dashboard');

  // Set active item based on current URL when component mounts
  useEffect(() => {
    const path = window.location.pathname;
    if (path === '/admin') {
      setActiveItem('dashboard');
    } else if (path.includes('/admin/orders')) {
      setActiveItem('orders');
    } else if (path.includes('/admin/products')) {
      setActiveItem('products');
    } else if (path.includes('/admin/categories')) {
      setActiveItem('categories');
    } else if (path.includes('/admin/customers')) {
      setActiveItem('customers');
    } else if (path.includes('/admin/locations')) {
      setActiveItem('locations');
    } else if (path.includes('/admin/delivery-boys')) {
      setActiveItem('delivery-boys');
    } else if (path.includes('/admin/coupons')) {
      setActiveItem('coupons');
    } else if (path.includes('/admin/delivery-fees')) {
      setActiveItem('delivery-fees');
    } else if (path.includes('/admin/payment-methods')) {
      setActiveItem('payment-methods');
    } else if (path.includes('/admin/analytics')) {
      setActiveItem('analytics');
    } else if (path.includes('/admin/settings')) {
      setActiveItem('settings');
    } else if (path.includes('/admin/help')) {
      setActiveItem('help');
    }
  }, []);

  // Sidebar navigation items
  const navigationItems = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: 'dashboard',
      href: '/admin'
    },
    {
      id: 'orders',
      name: 'Orders',
      icon: 'receipt_long',
      href: '/admin/orders',
      badge: '14'
    },
    {
      id: 'products',
      name: 'Books',
      icon: 'menu_book',
      href: '/admin/products'
    },
    {
      id: 'categories',
      name: 'Categories',
      icon: 'category',
      href: '/admin/categories'
    },
    {
      id: 'locations',
      name: 'Locations',
      icon: 'place',
      href: '/admin/locations'
    },
    {
      id: 'delivery-boys',
      name: 'Distribution Partners',
      icon: 'local_shipping',
      href: '/admin/delivery-boys'
    },
    {
      id: 'coupons',
      name: 'Discount Coupons',
      icon: 'local_offer',
      href: '/admin/coupons'
    },
    {
      id: 'delivery-fees',
      name: 'Shipping Fees',
      icon: 'payments',
      href: '/admin/delivery-fees'
    },
    {
      id: 'payment-methods',
      name: 'Payment Methods',
      icon: 'credit_card',
      href: '/admin/payment-methods'
    },
    {
      id: 'customers',
      name: 'Customers',
      icon: 'people',
      href: '/admin/customers'
    },

  ];

  // Secondary navigation items
  const secondaryItems = [
    {
      id: 'settings',
      name: 'Settings',
      icon: 'settings',
      href: '/admin/settings'
    },
    {
      id: 'help',
      name: 'Help Center',
      icon: 'help_outline',
      href: '/admin/help'
    }
  ];

  // Handle navigation item click
  const handleNavClick = (itemId) => {
    setActiveItem(itemId);
    if (isMobileMenuOpen && closeMobileMenu) {
      closeMobileMenu();
    }
  };

  return (
    <aside className={`bg-white border-r border-gray-200 flex-shrink-0 h-screen fixed left-0 top-0 z-40 w-64 lg:block transition-transform duration-300 ease-in-out ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`}>
      {/* Mobile Close Button */}
      <div className="lg:hidden absolute right-4 top-4">
        <button
          onClick={closeMobileMenu}
          className="text-gray-500 hover:text-gray-600 p-2"
          aria-label="Close menu"
        >
          <span className="material-icons-round">close</span>
        </button>
      </div>

      {/* Logo */}
      <div className="flex items-center justify-center h-16 border-b border-gray-200">
        <a href="/" className="flex items-center">
          <div className="w-8 h-8 rounded-md overflow-hidden mr-2">
            <img
              src="/images/sreekarpublishers-logo.jpeg"
              alt="Sreekar Publishers Logo"
              width="32"
              height="32"
              className="w-full h-full object-cover"
            />
          </div>
          <span className="font-bold text-xl text-gray-900">Sreekar Publishers</span>
        </a>
      </div>

      {/* Navigation */}
      <nav className="mt-5 flex-1 px-3 space-y-1">
        <div className="space-y-1">
          {navigationItems.map((item) => (
            <a
              key={item.id}
              href={item.href}
              onClick={() => handleNavClick(item.id)}
              className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                activeItem === item.id
                  ? 'bg-orange-50 text-orange-700'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <span className={`material-icons-round mr-3 flex-shrink-0 h-6 w-6 ${
                activeItem === item.id ? 'text-orange-500' : 'text-gray-500 group-hover:text-gray-600'
              }`}>
                {item.icon}
              </span>
              <span className="flex-1">{item.name}</span>
              {item.badge && (
                <span className={`ml-3 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  activeItem === item.id ? 'bg-orange-200 text-orange-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {item.badge}
                </span>
              )}
            </a>
          ))}
        </div>

        {/* Secondary Navigation Section */}
        <div className="mt-10 pt-6 border-t border-gray-200">
          <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
            Support
          </h3>
          <div className="mt-2 space-y-1">
            {secondaryItems.map((item) => (
              <a
                key={item.id}
                href={item.href}
                onClick={() => handleNavClick(item.id)}
                className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg ${
                  activeItem === item.id
                    ? 'bg-orange-50 text-orange-700'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <span className={`material-icons-round mr-3 flex-shrink-0 h-6 w-6 ${
                  activeItem === item.id ? 'text-orange-500' : 'text-gray-500 group-hover:text-gray-600'
                }`}>
                  {item.icon}
                </span>
                {item.name}
              </a>
            ))}
          </div>
        </div>
      </nav>

      {/* User Profile Section */}
      <div className="border-t border-gray-200 p-4 mt-auto">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center text-white font-medium">
              A
            </div>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-900">Admin User</p>
            <a href="/admin/profile" className="text-xs font-medium text-gray-500 hover:text-orange-600">
              View profile
            </a>
          </div>
          <button className="ml-auto p-1 text-gray-500 hover:text-gray-600">
            <span className="material-icons-round">logout</span>
          </button>
        </div>
      </div>
    </aside>
  );
}