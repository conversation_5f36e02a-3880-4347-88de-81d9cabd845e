---
import MainLayout from "../layouts/MainLayout.astro";
---

<MainLayout
  title="My Favorites - Sreekar Publishers"
  headerTitle="My Favorites"
  showHeader={true}
  showBackButton={true}
>
  <!-- Professional Header Section -->
  <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="bg-red-100 p-3 rounded-xl">
            <span class="material-icons-round text-red-600 text-2xl">favorite</span>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">My Favorites</h1>
            <p class="text-gray-600 text-sm mt-1">Your saved study materials</p>
          </div>
        </div>
        <div class="hidden sm:flex items-center space-x-4">
          <div class="bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
            <span class="text-sm text-gray-500">Total Items:</span>
            <span class="font-semibold text-gray-900 ml-1" id="favorites-count">0</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Loading State -->
    <div id="loading-state" class="flex items-center justify-center py-12">
      <div class="flex flex-col items-center space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p class="text-gray-500">Loading your favorites...</p>
      </div>
    </div>

    <!-- Favorites Grid -->
    <div id="favorites-container" class="hidden">
      <!-- Filter and Sort Bar -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div class="flex items-center space-x-4">
            <div class="relative">
              <select id="category-filter" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="all">All Categories</option>
                <option value="6th-grade">6th Grade</option>
                <option value="7th-grade">7th Grade</option>
                <option value="8th-grade">8th Grade</option>
                <option value="9th-grade">9th Grade</option>
                <option value="10th-grade">10th Grade</option>
              </select>
              <span class="material-icons-round absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm pointer-events-none">expand_more</span>
            </div>
            <div class="relative">
              <select id="sort-filter" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="newest">Recently Added</option>
                <option value="name">Name (A-Z)</option>
                <option value="price-low">Price (Low to High)</option>
                <option value="price-high">Price (High to Low)</option>
              </select>
              <span class="material-icons-round absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm pointer-events-none">expand_more</span>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button id="clear-all-btn" class="text-red-600 hover:text-red-700 text-sm font-medium px-3 py-2 rounded-lg hover:bg-red-50 transition-colors">
              <span class="material-icons-round text-sm mr-1">clear_all</span>
              Clear All
            </button>
          </div>
        </div>
      </div>

      <!-- Favorites Grid -->
      <div id="favorites-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <!-- Favorites will be loaded here -->
      </div>
    </div>

    <!-- Empty State -->
    <div class="hidden" id="empty-favorites">
      <div class="text-center py-16">
        <div class="bg-gray-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
          <span class="material-icons-round text-4xl text-gray-400">favorite_border</span>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No favorites yet</h3>
        <p class="text-gray-500 mb-8 max-w-md mx-auto">
          Start exploring our educational materials and save your favorites for easy access later.
        </p>
        <a href="/" class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl">
          <span class="material-icons-round mr-2">school</span>
          Browse Study Materials
        </a>
      </div>
    </div>
  </div>

<!-- Ensure favorites utils is loaded -->
<script is:inline src="/scripts/favorites-utils.js"></script>

<script is:inline>
  // Professional Favorites Page Implementation
  console.log('Professional Favorites script loaded');

  // Global state
  let allFavorites = [];
  let filteredFavorites = [];
  let currentCategory = 'all';
  let currentSort = 'newest';

  // Function to initialize favorites page
  function initFavoritesPage() {
    console.log('Initializing professional favorites page');
    const favoritesContainer = document.getElementById('favorites-container');
    const favoritesGrid = document.getElementById('favorites-grid');
    const emptyFavorites = document.getElementById('empty-favorites');
    const loadingState = document.getElementById('loading-state');

    if (!favoritesContainer || !favoritesGrid) {
      console.error('Required elements not found');
      return;
    }

    // Initialize filter and sort controls
    initializeControls();

    // Check if FavoritesUtils is already available
    if (window.FavoritesUtils) {
      console.log('FavoritesUtils already available, loading favorites');
      loadFavorites();
    } else {
      console.log('FavoritesUtils not available, waiting for it to load');
      // Listen for the favorites-utils-ready event
      window.addEventListener('favorites-utils-ready', function() {
        console.log('FavoritesUtils is now ready, loading favorites');
        loadFavorites();
      });
    }

    // Listen for changes in favorites
    window.addEventListener('favorites-updated', loadFavorites);
  }

  // Initialize filter and sort controls
  function initializeControls() {
    const categoryFilter = document.getElementById('category-filter');
    const sortFilter = document.getElementById('sort-filter');
    const clearAllBtn = document.getElementById('clear-all-btn');

    if (categoryFilter) {
      categoryFilter.addEventListener('change', (e) => {
        currentCategory = e.target.value;
        filterAndSortFavorites();
      });
    }

    if (sortFilter) {
      sortFilter.addEventListener('change', (e) => {
        currentSort = e.target.value;
        filterAndSortFavorites();
      });
    }

    if (clearAllBtn) {
      clearAllBtn.addEventListener('click', clearAllFavorites);
    }
  }

  // Filter and sort favorites
  function filterAndSortFavorites() {
    // Filter by category
    if (currentCategory === 'all') {
      filteredFavorites = [...allFavorites];
    } else {
      filteredFavorites = allFavorites.filter(item => {
        const categoryName = item.category_name?.toLowerCase() || '';
        return categoryName.includes(currentCategory.replace('-', ' '));
      });
    }

    // Sort favorites
    switch (currentSort) {
      case 'name':
        filteredFavorites.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'price-low':
        filteredFavorites.sort((a, b) => parseFloat(a.price || 0) - parseFloat(b.price || 0));
        break;
      case 'price-high':
        filteredFavorites.sort((a, b) => parseFloat(b.price || 0) - parseFloat(a.price || 0));
        break;
      case 'newest':
      default:
        // Keep original order (newest first)
        break;
    }

    renderFavorites();
  }

  // Make sure FavoritesUtils is loaded
  const loadFavoritesUtils = () => {
    return new Promise((resolve) => {
      if (window.FavoritesUtils) {
        resolve(window.FavoritesUtils);
      } else {
        // Create a script element if FavoritesUtils isn't available
        const script = document.createElement('script');
        script.src = '/scripts/favorites-utils.js';
        script.onload = () => {
          if (window.FavoritesUtils) {
            resolve(window.FavoritesUtils);
          } else {
            resolve(null); // Failed to load
          }
        };
        script.onerror = () => resolve(null);
        document.head.appendChild(script);
      }
    });
  };

  async function loadFavorites() {
    console.log('loadFavorites function called');
    const loadingState = document.getElementById('loading-state');
    const favoritesContainer = document.getElementById('favorites-container');
    const emptyFavorites = document.getElementById('empty-favorites');

    // Show loading state
    if (loadingState) loadingState.classList.remove('hidden');
    if (favoritesContainer) favoritesContainer.classList.add('hidden');
    if (emptyFavorites) emptyFavorites.classList.add('hidden');

    // Try to get FavoritesUtils directly first
    let favUtils = window.FavoritesUtils;

    // If not available, try to load it
    if (!favUtils) {
      console.log('FavoritesUtils not directly available, trying to load it');
      favUtils = await loadFavoritesUtils();
    }

    if (!favUtils) {
      console.error('FavoritesUtils not found');
      showError('Unable to load favorites. Please refresh the page.');
      return;
    }

    console.log('FavoritesUtils loaded successfully');
    const favorites = favUtils.getFavorites();
    console.log('Loaded favorites:', favorites);

    // Hide loading state
    if (loadingState) loadingState.classList.add('hidden');

    if (!favorites || favorites.length === 0) {
      console.log('No favorites found, showing empty state');
      if (emptyFavorites) {
        emptyFavorites.classList.remove('hidden');
      }
      updateFavoritesCount(0);
      return;
    }

    console.log('Found favorites, showing container');
    allFavorites = favorites;
    updateFavoritesCount(favorites.length);

    if (favoritesContainer) {
      favoritesContainer.classList.remove('hidden');
    }

    // Apply current filters and sorting
    filterAndSortFavorites();
  }

  // Render favorites in the grid
  function renderFavorites() {
    const favoritesGrid = document.getElementById('favorites-grid');
    if (!favoritesGrid) return;

    // Clear previous content
    favoritesGrid.innerHTML = '';

    // Add each favorite item to the grid
    filteredFavorites.forEach(product => {
      console.log('Adding product to favorites:', product);
      const card = document.createElement('div');
      card.className = 'bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 group';
      card.dataset.id = product.id;

      // Use url_slug if available, otherwise fall back to slug
      const productSlug = product.url_slug || product.slug || product.id;

      card.innerHTML = `
        <div class="relative">
          <a href="/product/${productSlug}" class="block">
            <div class="aspect-w-16 aspect-h-12 overflow-hidden">
              <img
                src="${product.image_url || '/images/placeholder-book.jpg'}"
                alt="${product.name}"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                loading="lazy"
              />
            </div>
            ${product.category_name ? `
              <div class="absolute top-3 left-3">
                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  ${product.category_name}
                </span>
              </div>
            ` : ''}
          </a>
          <button class="remove-favorite absolute top-3 right-3 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110">
            <span class="material-icons-round text-red-500 text-lg">favorite</span>
          </button>
        </div>
        <div class="p-4">
          <a href="/product/${productSlug}" class="block">
            <h3 class="font-semibold text-gray-900 text-sm mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">${product.name}</h3>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <span class="text-lg font-bold text-gray-900">₹${product.price}</span>
                ${product.old_price ? `<span class="text-sm text-gray-500 line-through">₹${product.old_price}</span>` : ''}
              </div>
              ${product.rating ? `
                <div class="flex items-center space-x-1">
                  <span class="material-icons-round text-yellow-400 text-sm">star</span>
                  <span class="text-xs text-gray-600">${product.rating}</span>
                </div>
              ` : ''}
            </div>
          </a>
        </div>
      `;

      favoritesGrid.appendChild(card);
    });

    // Add event listeners to remove buttons
    document.querySelectorAll('.remove-favorite').forEach(button => {
      button.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();
        const card = button.closest('[data-id]');
        const productId = card.dataset.id;

        const favUtils = await loadFavoritesUtils();
        if (!favUtils) {
          showToast('Unable to remove favorite. Please try again.');
          return;
        }

        // Remove from favorites
        favUtils.removeFavorite(productId);

        // Update local state
        allFavorites = allFavorites.filter(item => item.id != productId);
        updateFavoritesCount(allFavorites.length);

        // Animate the removal
        card.classList.add('opacity-0', 'scale-95');
        card.style.transition = 'all 300ms ease-out';

        setTimeout(() => {
          // Re-filter and render
          filterAndSortFavorites();

          // Check if no favorites left
          if (allFavorites.length === 0) {
            const favoritesContainer = document.getElementById('favorites-container');
            const emptyFavorites = document.getElementById('empty-favorites');
            if (favoritesContainer) favoritesContainer.classList.add('hidden');
            if (emptyFavorites) emptyFavorites.classList.remove('hidden');
          }
        }, 300);

        // Show toast
        showToast('Removed from favorites');

        if ('vibrate' in navigator) {
          navigator.vibrate(40);
        }
      });
    });
  }

  // Update favorites count display
  function updateFavoritesCount(count) {
    const favoritesCount = document.getElementById('favorites-count');
    if (favoritesCount) {
      favoritesCount.textContent = count;
    }
  }

  // Clear all favorites
  async function clearAllFavorites() {
    if (!confirm('Are you sure you want to remove all favorites?')) {
      return;
    }

    const favUtils = await loadFavoritesUtils();
    if (!favUtils) {
      showToast('Unable to clear favorites. Please try again.');
      return;
    }

    // Clear all favorites
    allFavorites.forEach(item => {
      favUtils.removeFavorite(item.id);
    });

    // Update state
    allFavorites = [];
    filteredFavorites = [];
    updateFavoritesCount(0);

    // Show empty state
    const favoritesContainer = document.getElementById('favorites-container');
    const emptyFavorites = document.getElementById('empty-favorites');
    if (favoritesContainer) favoritesContainer.classList.add('hidden');
    if (emptyFavorites) emptyFavorites.classList.remove('hidden');

    showToast('All favorites cleared');
  }

  // Display toast messages
  function showToast(message) {
    let toastContainer = document.querySelector('.toast-container');

    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.className = 'toast-container fixed bottom-24 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-5';
      document.body.appendChild(toastContainer);
    }

    const toast = document.createElement('div');
    toast.className = 'bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-xl opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md';

    toast.innerHTML = `
      <span class="material-icons-round text-base mr-2">favorite</span>
      <span>${message}</span>
    `;

    toastContainer.appendChild(toast);

    setTimeout(() => {
      toast.classList.remove('opacity-0', 'translate-y-4');
      toast.classList.add('opacity-95');
    }, 10);

    setTimeout(() => {
      toast.classList.add('opacity-0', 'translate-y-4');
      setTimeout(() => {
        toast.remove();
      }, 300);
    }, 2500);
  }

  function showError(message) {
    const loadingState = document.getElementById('loading-state');
    const favoritesContainer = document.getElementById('favorites-container');
    const emptyFavorites = document.getElementById('empty-favorites');

    // Hide loading state
    if (loadingState) loadingState.classList.add('hidden');
    if (favoritesContainer) favoritesContainer.classList.add('hidden');

    // Show error in empty state
    if (emptyFavorites) {
      emptyFavorites.innerHTML = `
        <div class="text-center py-16">
          <div class="bg-red-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
            <span class="material-icons-round text-4xl text-red-400">error_outline</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Error Loading Favorites</h3>
          <p class="text-gray-500 mb-8 max-w-md mx-auto">${message}</p>
          <button onclick="location.reload()" class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors">
            <span class="material-icons-round mr-2">refresh</span>
            Try Again
          </button>
        </div>
      `;
      emptyFavorites.classList.remove('hidden');
    }
  }

  // Initialize on DOMContentLoaded
  document.addEventListener('DOMContentLoaded', initFavoritesPage);

  // Initialize on Astro page transitions
  document.addEventListener('astro:page-load', initFavoritesPage);
</script>
</MainLayout>