---
import MainLayout from "../layouts/MainLayout.astro";
import { getCategories } from "../db/database";

// Disable prerendering to use server-side rendering with Cloudflare D1
export const prerender = false;

// Define fallback categories in case database is unavailable
const fallbackCategories = [
  { id: 1, name: "<PERSON><PERSON>", icon: "🍟", color: "#FF9F80" },
  { id: 2, name: "Cookies", icon: "🍪", color: "#E0C094" },
  { id: 3, name: "Chocolates", icon: "🍫", color: "#C79F7A" },
  { id: 4, name: "Drinks", icon: "🥤", color: "#8ECAE6" },
  { id: 5, name: "Nuts", icon: "🥜", color: "#D9BF77" },
];

// Safely fetch categories from database with fallback
let dbCategories = [];
try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    dbCategories = await getCategories(Astro.locals.runtime.env);
  } else {
    console.warn(
      "Runtime environment not available, using fallback categories"
    );
    dbCategories = fallbackCategories;
  }
} catch (error) {
  console.error("Error fetching categories:", error);
  dbCategories = fallbackCategories;
}

// Create a combined list with "All Products" at the beginning
const categories = [
  { id: "all", name: "All Products" },
  ...dbCategories.map((cat) => ({ id: cat.id.toString(), name: cat.name })),
];

// Sort options
const sortOptions = [
  // { id: "popular", name: "Most Popular" },
  { id: "price-asc", name: "Price: Low to High" },
  { id: "price-desc", name: "Price: High to Low" },
  { id: "newest", name: "Newest First" },
];

// Initial state - products will be loaded via API
const initialCategory = "all";
const initialSort = "popular";
---

<MainLayout
  title="Browse Products - Sreekar Publishers"
  headerTitle="Products"
  showHeader={true}
  showBackButton={false}
>
  <div
    class="relative"
    id="products-container"
    data-category="all"
    data-sort="popular"
    data-page="1"
  >
    <!-- Product specific header controls (filters, sorting) -->
    <div class="sticky top-[45px] bg-white z-20 shadow-sm">
      <div class="flex items-center gap-3 px-4 py-3.5">
        <div class="relative flex-1">
          <input
            type="text"
            id="product-search"
            placeholder="Search products..."
            class="w-full pl-10 pr-4 py-3 bg-white rounded-xl text-sm border border-gray-200 focus:ring-2 focus:ring-[#5466F7] focus:border-transparent outline-none shadow-sm transition-all"
          />
          <span
            class="material-icons-round absolute left-3.5 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"
            >search</span
          >
        </div>

        <button
          id="filter-button"
          class="inline-flex items-center justify-center p-3 rounded-xl bg-white border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm"
          aria-label="Filter products"
        >
          <span class="material-icons-round text-gray-700">tune</span>
        </button>

        <button
          id="sort-button"
          class="inline-flex items-center justify-center p-3 rounded-xl bg-white border border-gray-200 hover:bg-gray-50 transition-colors shadow-sm"
          aria-label="Sort products"
        >
          <span class="material-icons-round text-gray-700">sort</span>
        </button>
      </div>

      <!-- Category tabs with improved design -->
      <!-- <div
        class="flex overflow-x-auto scrollbar-hide px-4 pb-3 pt-2 category-tabs gap-2"
      >
        {
          categories.map((category) => (
            <button
              class={`whitespace-nowrap px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 data-category-btn
              ${
                category.id === initialCategory // Use initialCategory for initial active state
                  ? "bg-[#5466F7] text-white shadow-md shadow-blue-200"
                  : "text-gray-700 border border-gray-200 bg-white hover:border-[#5466F7]/30 hover:bg-blue-50/30 active:bg-gray-100"
              }`}
              data-category={category.id}
            >
              {category.name}
            </button>
          ))
        }
      </div> -->
    </div>

    <!-- Products Grid with improved card design -->
    <div class="px-4 py-5">
      <!-- New Categories Slider without icons -->
      <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-3 px-1">Categories</h2>
        <div class="overflow-x-auto scrollbar-hide pb-2">
          <div class="flex space-x-3">
            {
              categories.map((category) => (
                <div 
                  class="category-slide flex-shrink-0 cursor-pointer" 
                  data-category={category.id}
                >
                  <div class={`px-5 py-3 rounded-xl border border-gray-200 transition-all hover:shadow-md ${category.id === initialCategory ? "border-[#5466F7] bg-blue-50/30 shadow-sm" : "bg-white hover:border-[#5466F7]/20"}`}>
                    <p class={`whitespace-nowrap font-medium ${category.id === initialCategory ? "text-[#5466F7]" : "text-gray-700"}`}>
                      {category.name}
                    </p>
                  </div>
                </div>
              ))
            }
          </div>
        </div>
      </div>

      <div id="products-grid" class="grid grid-cols-2 gap-4 mb-20">
        <!-- Products will be loaded via API -->
      </div>

      <!-- Skeleton loader for initial load -->
      <div id="skeleton-loader" class="grid grid-cols-2 gap-4 mb-20">
        {
          Array(6)
            .fill(0)
            .map(() => (
              <div class="product-skeleton bg-white rounded-xl overflow-hidden shadow-md border border-gray-100">
                <div class="w-full aspect-square bg-gray-100 animate-pulse relative">
                  <div class="absolute top-3 left-3 h-5 w-12 bg-gray-200 rounded-full animate-pulse"></div>
                </div>
                <div class="p-4">
                  <div class="h-2 w-16 bg-gray-200 rounded-full animate-pulse mb-3"></div>
                  <div class="h-4 w-full bg-gray-200 rounded-full animate-pulse mb-2"></div>
                  <div class="h-4 w-3/4 bg-gray-200 rounded-full animate-pulse mb-3"></div>
                  <div class="flex justify-between items-center pt-2">
                    <div class="h-5 w-16 bg-gray-200 rounded-full animate-pulse"></div>
                    <div class="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))
        }
      </div>

      <!-- No results message -->
      <div
        id="no-results"
        class="hidden flex items-center justify-center flex-col py-12"
      >
        <div
          class="w-20 h-20 bg-gray-50 rounded-full flex items-center justify-center mb-4 border border-gray-100"
        >
          <span class="material-icons-round text-gray-400 text-3xl"
            >search_off</span
          >
        </div>
        <h3 class="text-lg font-semibold text-gray-700 mb-2">
          No products found
        </h3>
        <p class="text-gray-500 text-center max-w-xs">
          We couldn't find any products matching your criteria. Try adjusting
          your filters or search.
        </p>
      </div>

      <!-- Loading indicator for subsequent loads -->
      <div
        id="loading-indicator"
        class="hidden items-center justify-center py-8"
      >
        <div
          class="w-10 h-10 border-3 border-gray-200 border-t-[#5466F7] rounded-full animate-spin"
        >
        </div>
      </div>

      <!-- Load more button (can be used as a trigger or fallback) -->
      <div
        id="load-more-container"
        class="flex justify-center mt-6 mb-12 hidden"
      >
        <button
          id="load-more-btn"
          class="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-xl font-medium transition-colors flex items-center gap-2"
        >
          <span>Load More</span>
          <span class="material-icons-round text-sm">expand_more</span>
        </button>
      </div>
      <!-- Sentinel element for Intersection Observer -->
      <div id="infinite-scroll-sentinel" class="h-10"></div>
    </div>
  </div>

  <!-- Sort options modal - Enhanced design -->
  <div
    id="sort-modal"
    class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm z-50 hidden transition-opacity opacity-0 duration-300"
  >
    <div
      class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-[80vh] overflow-y-auto transform translate-y-full transition-transform duration-300 shadow-xl"
    >
      <div class="flex justify-center pt-3 pb-1">
        <div class="w-12 h-1 bg-gray-300 rounded-full"></div>
      </div>
      <div class="p-5">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-800 flex items-center">
            <span class="material-icons-round text-[#5466F7] mr-2">sort</span>
            Sort By
          </h2>
          <button
            id="close-sort-modal"
            class="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <span class="material-icons-round text-gray-500">close</span>
          </button>
        </div>
      </div>
      <div class="px-2">
        {
          sortOptions.map((option) => (
            <button
              class={`w-full text-left py-4 px-5 hover:bg-blue-50/40 data-sort-option transition-colors relative ${option.id === initialSort ? "bg-blue-50" : ""}`}
              data-sort={option.id}
            >
              <div class="flex items-center justify-between">
                <span class="font-medium text-gray-700">{option.name}</span>
                <span
                  class={`material-icons-round sort-check-icon text-[#5466F7] ${option.id === initialSort ? "" : "opacity-0"}`}
                >
                  check_circle
                </span>
              </div>
            </button>
          ))
        }
      </div>
      <div class="p-5 border-t border-gray-100 mt-2">
        <button
          id="apply-sort"
          class="w-full py-3.5 bg-[#5466F7] text-white rounded-xl font-medium hover:bg-[#4555e2] transition-all shadow-md flex items-center justify-center"
        >
          <span class="material-icons-round mr-2">check</span>
          Apply Sort
        </button>
      </div>
    </div>
  </div>

  <!-- Filter modal - Enhanced design -->
  <div
    id="filter-modal"
    class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm z-50 hidden transition-opacity opacity-0 duration-300"
  >
    <div
      class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-[80vh] overflow-y-auto transform translate-y-full transition-transform duration-300 shadow-xl"
    >
      <div class="flex justify-center pt-3 pb-1">
        <div class="w-12 h-1 bg-gray-300 rounded-full"></div>
      </div>
      <div class="p-5">
        <div class="flex justify-between items-center mb-5">
          <h2 class="text-xl font-semibold text-gray-800 flex items-center">
            <span class="material-icons-round text-[#5466F7] mr-2">tune</span>
            Filter Products
          </h2>
          <button
            id="close-filter-modal"
            class="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <span class="material-icons-round text-gray-500">close</span>
          </button>
        </div>

        <div class="py-2 space-y-8">
          <div>
            <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
              <span class="material-icons-round text-[#5466F7] mr-2 text-base">category</span>
              Categories
            </h3>
            <div class="grid grid-cols-2 gap-3">
              {
                categories.map((category) => (
                  <label class="flex items-center gap-3 p-3.5 rounded-xl border border-gray-200 cursor-pointer hover:border-[#5466F7]/30 hover:bg-blue-50/30 transition-colors">
                    <input
                      type="checkbox"
                      name="category"
                      value={category.id}
                      class="rounded text-[#5466F7] focus:ring-[#5466F7] h-5 w-5 filter-category-checkbox"
                    />
                    <span class="text-gray-700 font-medium">
                      {category.name}
                    </span>
                  </label>
                ))
              }
            </div>
          </div>

          <div>
            <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
              <span class="material-icons-round text-[#5466F7] mr-2 text-base">payments</span>
              Price Range
            </h3>
            <div class="px-2">
              <input
                type="range"
                min="0"
                max="100"
                value="100"
                class="w-full accent-[#5466F7]"
                id="price-range"
              />
              <div
                class="flex justify-between text-sm text-gray-600 mt-3 font-medium"
              >
                <span>₹0</span>
                <span id="price-value" class="text-[#5466F7]">₹100</span>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
              <span class="material-icons-round text-[#5466F7] mr-2 text-base">local_offer</span>
              Product Tags
            </h3>
            <div class="space-y-4">
              <label
                class="flex items-center justify-between p-3.5 rounded-xl border border-gray-200 cursor-pointer hover:border-[#5466F7]/30 hover:bg-blue-50/30 transition-colors"
              >
                <div class="flex items-center gap-3">
                  <span class="material-icons-round text-[#2EC4B6] text-base">new_releases</span>
                  <span class="text-gray-700 font-medium">New Arrivals</span>
                </div>
                <input
                  type="checkbox"
                  id="filter-new_arrivals"
                  class="rounded text-[#5466F7] focus:ring-[#5466F7] h-5 w-5 filter-tag-checkbox"
                />
              </label>
              <label
                class="flex items-center justify-between p-3.5 rounded-xl border border-gray-200 cursor-pointer hover:border-[#5466F7]/30 hover:bg-blue-50/30 transition-colors"
              >
                <div class="flex items-center gap-3">
                  <span class="material-icons-round text-[#FF6B35] text-base">sell</span>
                  <span class="text-gray-700 font-medium">On Sale</span>
                </div>
                <input
                  type="checkbox"
                  id="filter-on_sale"
                  class="rounded text-[#5466F7] focus:ring-[#5466F7] h-5 w-5 filter-tag-checkbox"
                />
              </label>
            </div>
          </div>

          <div class="flex gap-4 mt-8 pt-4 border-t border-gray-100">
            <button
              id="reset-filters"
              class="flex-1 py-3.5 border border-gray-200 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-colors flex items-center justify-center"
            >
              <span class="material-icons-round mr-2 text-base">restart_alt</span>
              Reset
            </button>
            <button
              id="apply-filters"
              class="flex-1 py-3.5 bg-[#5466F7] text-white rounded-xl font-medium hover:bg-[#4555e2] transition-colors shadow-md flex items-center justify-center"
            >
              <span class="material-icons-round mr-2 text-base">check</span>
              Apply
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script is:inline>
    document.addEventListener("DOMContentLoaded", () => {
      // DOM Elements
      const productsGrid = document.getElementById("products-grid");
      const skeletonLoader = document.getElementById("skeleton-loader");
      const noResultsMessage = document.getElementById("no-results");
      const loadingIndicator = document.getElementById("loading-indicator");
      const sortButton = document.getElementById("sort-button");
      const filterButton = document.getElementById("filter-button");
      const sortModal = document.getElementById("sort-modal");
      const filterModal = document.getElementById("filter-modal");
      const closeSortModal = document.getElementById("close-sort-modal");
      const closeFilterModal = document.getElementById("close-filter-modal");
      const sortOptions = document.querySelectorAll(".data-sort-option");
      const categoryButtons = document.querySelectorAll(".data-category-btn");
      const priceRange = document.getElementById("price-range");
      const priceValue = document.getElementById("price-value");
      const resetFiltersBtn = document.getElementById("reset-filters");
      const applyFiltersBtn = document.getElementById("apply-filters");
      const applySortBtn = document.getElementById("apply-sort");
      const searchInput = document.getElementById("product-search");
      const productsContainer = document.getElementById("products-container");
      const loadMoreContainer = document.getElementById("load-more-container");
      const loadMoreBtn = document.getElementById("load-more-btn");
      const infiniteScrollSentinel = document.getElementById(
        "infinite-scroll-sentinel"
      );

      // State
      let currentCategory =
        productsContainer.getAttribute("data-category") || "all";
      let currentSort =
        productsContainer.getAttribute("data-sort") || "popular";
      let currentPage = 1;
      let currentSearchQuery = "";
      let isLoading = false;
      let hasMoreProducts = true;
      let activeFilters = {
        category: currentCategory, // Use single category from tabs or 'all'
        tags: [],
        minPrice: 0,
        maxPrice: 100, // Match initial range slider value
      };
      let selectedSort = currentSort; // Temporary holder for sort modal selection

      // Store previous filter state to avoid unnecessary API calls
      let previousFilterState = JSON.stringify({
        category: activeFilters.category,
        tags: activeFilters.tags,
        minPrice: activeFilters.minPrice,
        maxPrice: activeFilters.maxPrice,
        sort: currentSort,
        search: currentSearchQuery
      });

      // Check if filters have actually changed before fetching
      const haveFiltersChanged = () => {
        const currentState = JSON.stringify({
          category: activeFilters.category,
          tags: activeFilters.tags,
          minPrice: activeFilters.minPrice,
          maxPrice: activeFilters.maxPrice,
          sort: currentSort,
          search: currentSearchQuery
        });
        
        const changed = previousFilterState !== currentState;
        if (changed) {
          previousFilterState = currentState;
        }
        return changed;
      };

      // --- API Fetching ---
      const fetchProducts = async (reset = false) => {
        if (isLoading) return;
        isLoading = true;

        // Show appropriate loading indicators
        if (reset) {
          currentPage = 1;
          productsGrid.innerHTML = ""; // Clear existing products
          noResultsMessage.classList.add("hidden");
          skeletonLoader.classList.remove("hidden");
          loadMoreContainer.classList.add("hidden");
          hasMoreProducts = true;
        } else {
          loadingIndicator.classList.remove("hidden");
          skeletonLoader.classList.add("hidden");
        }

        const limit = 12; // Products per page
        const params = new URLSearchParams({
          page: currentPage.toString(),
          limit: limit.toString(),
          sort: currentSort,
          search: currentSearchQuery,
          minPrice: activeFilters.minPrice.toString(),
          maxPrice: activeFilters.maxPrice.toString(),
        });

        // Add category only if it's not 'all'
        if (activeFilters.category !== "all") {
          params.append("category", activeFilters.category);
        }

        // Add tags if any are selected
        if (activeFilters.tags.length > 0) {
          params.append("tags", activeFilters.tags.join(","));
        }

        try {
          // Add cache-busting parameter for development
          // if (process.env.NODE_ENV === "development") {
          //   params.append("_", Date.now().toString());
          // }

          // Use fetch with AbortController for timeout control
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 15000); // 15-second timeout

          const response = await fetch(`/api/products?${params.toString()}`, {
            signal: controller.signal,
            headers: {
              "Accept": "application/json",
              "X-Requested-With": "XMLHttpRequest" 
            }
          });
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const data = await response.json();

          if (reset) {
            productsGrid.innerHTML = ""; // Ensure grid is empty before adding new results
          }

          if (data.products && data.products.length > 0) {
            // Create all product cards at once using DocumentFragment for better performance
            const fragment = document.createDocumentFragment();
            
            data.products.forEach((product) => {
              const productElement = createProductCard(product);
              fragment.appendChild(productElement);
            });
            
            productsGrid.appendChild(fragment);
            
            // Add event listeners after appending to DOM
            data.products.forEach((product) => {
              const productElement = document.querySelector(`[data-id="${product.id}"]`);
              if (productElement) {
                setupSwipeToWishlist(
                  productElement.querySelector(".product-image-container"),
                  product
                );
                productElement
                  .querySelector(".quick-add-btn")
                  ?.addEventListener("click", (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    addToCart(product);
                  });
              }
            });
            
            currentPage++;
            hasMoreProducts = data.hasMore;
            noResultsMessage.classList.add("hidden");
            productsGrid.classList.remove("hidden");
            skeletonLoader.classList.add("hidden");

            // If it was a search, show how many results were found
            if (currentSearchQuery && reset && data.resultsCount) {
              showNotification(`Found ${data.resultsCount} products matching "${currentSearchQuery}"`);
            }
          } else if (reset) {
            // No results found on initial load/reset
            noResultsMessage.classList.remove("hidden");
            skeletonLoader.classList.add("hidden");
            productsGrid.classList.add("hidden");
            
            // Show appropriate no results message for search vs filters
            const noResultsTitle = document.querySelector("#no-results h3");
            const noResultsText = document.querySelector("#no-results p");
            
            if (currentSearchQuery) {
              noResultsTitle.textContent = `No results for "${currentSearchQuery}"`;
              noResultsText.textContent = "Try a different search term or browse categories instead.";
            } else {
              noResultsTitle.textContent = "No products found";
              noResultsText.textContent = "We couldn't find any products matching your criteria. Try adjusting your filters.";
            }
          }

          // Update load more button/sentinel visibility
          if (hasMoreProducts) {
            loadMoreContainer.classList.remove("hidden");
            // Re-observe the sentinel for infinite scroll
            if (infiniteScrollSentinel && 'IntersectionObserver' in window) {
              observer.observe(infiniteScrollSentinel);
            }
          } else {
            loadMoreContainer.classList.add("hidden");
          }
          
        } catch (error) {
          console.error("Failed to fetch products:", error);
          
          // Handle different error types
          if (error.name === "AbortError") {
            showNotification("Request timed out. Please try again.", true);
          } else {
            showNotification("Error loading products. Please try again.", true);
          }
          
          if (reset) {
            noResultsMessage.classList.remove("hidden");
            productsGrid.classList.add("hidden");
            skeletonLoader.classList.add("hidden");
            
            // Update error message
            const noResultsTitle = document.querySelector("#no-results h3");
            const noResultsText = document.querySelector("#no-results p");
            noResultsTitle.textContent = "Couldn't load products";
            noResultsText.textContent = "There was a problem connecting to our servers. Please try again later.";
          }
        } finally {
          isLoading = false;
          loadingIndicator.classList.add("hidden");
          skeletonLoader.classList.add("hidden");
        }
      };

      // --- Product Card Creation with performance optimizations ---
      const createProductCard = (product) => {
        const div = document.createElement("div");
        div.className =
          "product-card bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg border border-gray-100 transition-all duration-300";
        div.dataset.id = product.id;
        
        // Use URL constructor for image fallback to ensure valid paths
        const imageUrl = product.image_url ? 
          new URL(product.image_url, window.location.origin).href : 
          "/placeholder-image.svg";
            
        // Format price properly with error handling  
        let priceDisplay = "₹0.00";
        try {
          const price = parseFloat(product.price || 0);
          priceDisplay = `₹${price.toFixed(2)}`;
        } catch (e) {
          console.error("Price formatting error:", e);
        }
        
        // Optimize HTML generation without blue background on images and relocated wishlist button
        div.innerHTML = `
          <a href="/products/${product.url_slug || product.id}" class="block">
            <div class="product-image-container relative overflow-hidden aspect-square">
              <img src="${imageUrl}" alt="${product.name}" 
                  loading="lazy"
                  class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105">
              ${product.is_new ? '<span class="absolute top-3 left-3 bg-[#2EC4B6] text-white text-xs font-medium px-2.5 py-1 rounded-full shadow-sm z-10">New</span>' : ""}
              ${product.on_sale ? '<span class="absolute top-3 right-3 bg-[#FF6B35] text-white text-xs font-medium px-2.5 py-1 rounded-full shadow-sm z-10">Sale</span>' : ""}
            </div>
          </a>
          <div class="p-4">
            ${product.category_name ? `<div class="text-xs text-gray-500 mb-1.5 font-medium">${product.category_name}</div>` : ""}
            
            <div class="flex justify-between items-start mb-2">
              <h3 class="font-medium text-gray-800 text-sm leading-tight line-clamp-2 flex-1">
                <a href="/products/${product.url_slug || product.id}" class="hover:text-[#5466F7] transition-colors">
                  ${product.name}
                </a>
              </h3>
              <button 
                class="wishlist-btn ml-2 w-8 h-8 rounded-full bg-gray-100 hover:bg-[#5466F7] hover:text-white text-gray-500 flex-shrink-0 flex items-center justify-center transition-all duration-200"
                aria-label="Add to wishlist">
                <span class="material-icons-round text-sm">favorite_border</span>
              </button>
            </div>
            
            <div class="flex justify-between items-center mt-3">
              <div class="flex items-center gap-1.5">
                <p class="font-bold text-[#5466F7]">${priceDisplay}</p>
                ${product.old_price ? `<p class="text-xs text-gray-400 line-through">₹${parseFloat(product.old_price).toFixed(2)}</p>` : ''}
              </div>
              <button 
                class="quick-add-btn w-9 h-9 rounded-full bg-gray-100 hover:bg-[#5466F7] hover:text-white text-gray-600 flex items-center justify-center transition-all duration-200 hover:shadow-md"
                aria-label="Add ${product.name} to cart">
                <span class="material-icons-round text-lg">add</span>
              </button>
            </div>
          </div>
        `;
        return div;
      };

      // --- UI Updates & Event Listeners ---

      // Update UI to reflect current category selection
      const updateCategoryUI = (categoryId) => {
        categoryButtons.forEach((button) => {
          const btnCategory = button.getAttribute("data-category");
          button.classList.remove(
            "bg-[#5466F7]",
            "text-white",
            "shadow-md",
            "shadow-blue-200"
          );
          button.classList.add(
            "text-gray-700",
            "border",
            "border-gray-200",
            "bg-gray-50"
          );
          if (btnCategory === categoryId) {
            button.classList.remove(
              "text-gray-700",
              "border",
              "border-gray-200",
              "bg-gray-50"
            );
            button.classList.add(
              "bg-[#5466F7]",
              "text-white",
              "shadow-md",
              "shadow-blue-200"
            );
          }
        });
        activeFilters.category = categoryId; // Update active filter
        productsContainer.setAttribute("data-category", categoryId); // Update container data (optional)
      };

      // Enhanced modal handling functions
      function openModal(modal) {
        modal.classList.remove("hidden");
        setTimeout(() => {
          modal.classList.add("opacity-100");
          modal
            .querySelector(".transform")
            .classList.remove("translate-y-full");
        }, 10);
      }

      function closeModal(modal) {
        modal.classList.remove("opacity-100");
        modal.querySelector(".transform").classList.add("translate-y-full");
        setTimeout(() => {
          modal.classList.add("hidden");
        }, 300);
      }

      // Update price value display
      if (priceRange && priceValue) {
        // Set initial value display
        priceValue.textContent = `$${priceRange.value}`;
        activeFilters.maxPrice = parseInt(priceRange.value); // Set initial filter value

        priceRange.addEventListener("input", (e) => {
          const value = parseInt(e.target.value);
          priceValue.textContent = `$${value}`;
          // Update filter value immediately for visual feedback, applied on Apply button
          // activeFilters.maxPrice = value; // Or update only on apply
        });
      }

      // Show/Hide modals
      sortButton.addEventListener("click", () => openModal(sortModal));
      filterButton.addEventListener("click", () => openModal(filterModal));
      closeSortModal.addEventListener("click", () => closeModal(sortModal));
      closeFilterModal.addEventListener("click", () => closeModal(filterModal));
      window.addEventListener("click", (e) => {
        if (e.target === sortModal) closeModal(sortModal);
        if (e.target === filterModal) closeModal(filterModal);
      });

      // Category filtering - Trigger API Fetch
      categoryButtons.forEach((button) => {
        button.addEventListener("click", () => {
          const categoryId = button.getAttribute("data-category");
          if (categoryId === activeFilters.category) return; // Avoid refetch if category hasn't changed

          if ("vibrate" in navigator) navigator.vibrate(40);
          updateCategoryUI(categoryId);
          fetchProducts(true); // Reset and fetch
          showNotification(
            `Showing ${categoryId === "all" ? "all products" : button.textContent.trim()}`
          );
        });
      });

      // Handle search input with improved debouncing
      if (searchInput) {
        let searchTimeout;
        let previousQuery = "";
        
        // Add clear button to search
        const searchContainer = searchInput.parentElement;
        const clearButton = document.createElement("button");
        clearButton.className = "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors hidden";
        clearButton.innerHTML = '<span class="material-icons-round text-sm">close</span>';
        clearButton.setAttribute("aria-label", "Clear search");
        searchContainer.appendChild(clearButton);
        
        // Show/hide clear button based on input
        searchInput.addEventListener("input", (e) => {
          clearTimeout(searchTimeout);
          const newQuery = e.target.value.trim();
          
          // Show/hide clear button
          if (newQuery) {
            clearButton.classList.remove("hidden");
          } else {
            clearButton.classList.add("hidden");
          }
          
          // Update search state
          searchTimeout = setTimeout(() => {
            if (newQuery !== previousQuery) {
              previousQuery = newQuery;
              currentSearchQuery = newQuery;
              
              // Show appropriate loading indication
              if (newQuery) {
                searchInput.classList.add("bg-blue-50/30");
              }
              
              fetchProducts(true); // Reset and fetch
            }
          }, 500); // Debounce search input
        });
        
        // Clear search when clicking clear button
        clearButton.addEventListener("click", () => {
          searchInput.value = "";
          clearButton.classList.add("hidden");
          if (currentSearchQuery) {
            currentSearchQuery = "";
            previousQuery = "";
            searchInput.classList.remove("bg-blue-50/30");
            fetchProducts(true); // Reset and fetch with empty search
            showNotification("Search cleared");
          }
        });
        
        // Clear on escape key
        searchInput.addEventListener("keydown", (e) => {
          if (e.key === "Escape" && searchInput.value) {
            searchInput.value = "";
            clearButton.classList.add("hidden");
            if (currentSearchQuery) {
              currentSearchQuery = "";
              previousQuery = "";
              searchInput.classList.remove("bg-blue-50/30");
              fetchProducts(true);
              showNotification("Search cleared");
            }
          }
        });
      }

      // Sort options - Update selection state
      sortOptions.forEach((option) => {
        option.addEventListener("click", () => {
          sortOptions.forEach((opt) => {
            opt.querySelector(".sort-check-icon").classList.add("opacity-0");
            opt.classList.remove("bg-blue-50");
          });
          option
            .querySelector(".sort-check-icon")
            .classList.remove("opacity-0");
          option.classList.add("bg-blue-50");
          selectedSort = option.dataset.sort; // Store selection temporarily
        });
      });

      // Apply sort - Trigger API Fetch
      applySortBtn.addEventListener("click", () => {
        if ("vibrate" in navigator) navigator.vibrate(40);
        if (currentSort !== selectedSort) {
          currentSort = selectedSort;
          productsContainer.setAttribute("data-sort", currentSort); // Update container data (optional)
          fetchProducts(true); // Reset and fetch
          const sortDisplayName =
            Array.from(sortOptions)
              .find((option) => option.dataset.sort === currentSort)
              ?.querySelector("span.font-medium").textContent ||
            "selected criteria";
          showNotification(`Products sorted by ${sortDisplayName}`);
        }
        closeModal(sortModal);
      });

      // Reset filters - Reset state and Trigger API Fetch
      resetFiltersBtn.addEventListener("click", () => {
        // Reset price range slider and value display
        priceRange.value = 100; // Reset to max
        priceValue.textContent = "$100";

        // Reset checkboxes in the filter modal
        document
          .querySelectorAll('#filter-modal input[type="checkbox"]')
          .forEach((checkbox) => {
            checkbox.checked = false;
          });

        // Reset activeFilters state
        activeFilters.tags = [];
        activeFilters.minPrice = 0;
        activeFilters.maxPrice = 100; // Reset to max

        // Note: We don't reset activeFilters.category here as it's controlled by tabs
        // If you want reset to go back to 'all', uncomment the next lines:
        // updateCategoryUI('all');

        showNotification("Filters reset");
        fetchProducts(true); // Refetch with reset filters
        closeModal(filterModal); // Optionally close modal on reset
      });

      // Apply filters - Trigger API Fetch
      applyFiltersBtn.addEventListener("click", () => {
        if ("vibrate" in navigator) navigator.vibrate(40);

        // Store previous values to check if anything actually changed
        const prevMaxPrice = activeFilters.maxPrice;
        const prevTags = [...activeFilters.tags];

        // Update activeFilters state from modal inputs
        activeFilters.maxPrice = parseInt(priceRange.value);
        activeFilters.minPrice = 0; // Assuming min is always 0 for this slider

        activeFilters.tags = [];
        document
          .querySelectorAll(".filter-tag-checkbox:checked")
          .forEach((checkbox) => {
            const tagName = checkbox.id
              .replace("filter-", "")
              .replace(/-/g, "_");
            activeFilters.tags.push(tagName);
          });

        // Only fetch if filters have changed
        if (haveFiltersChanged()) {
          fetchProducts(true); // Reset and fetch with new filters
          
          // Create a descriptive message about what filters were applied
          let filterMessage = "Filters applied";
          if (activeFilters.tags.length > 0) {
            const tagNames = activeFilters.tags.map(tag => 
              tag === "new_arrivals" ? "New Arrivals" : 
              tag === "on_sale" ? "On Sale" : tag
            ).join(", ");
            filterMessage = `Showing ${tagNames}`;
            if (activeFilters.maxPrice < 100) {
              filterMessage += ` under ₹${activeFilters.maxPrice}`;
            }
          } else if (activeFilters.maxPrice < 100) {
            filterMessage = `Showing products under ₹${activeFilters.maxPrice}`;
          }
          
          showNotification(filterMessage);
        } else {
          showNotification("No filter changes made");
        }
        
        closeModal(filterModal);
      });

      // --- Infinite Scrolling ---
      let observer; // Define observer at the top level

      // Function to setup intersection observer for infinite scroll
      const setupInfiniteScroll = () => {
        // Disconnect existing observer if any
        if (observer) {
          observer.disconnect();
        }

        // Only setup if browser supports IntersectionObserver
        if ('IntersectionObserver' in window && infiniteScrollSentinel) {
          observer = new IntersectionObserver(
            (entries) => {
              if (entries[0].isIntersecting && hasMoreProducts && !isLoading) {
                fetchProducts(false); // Fetch next page without resetting
              }
            },
            { 
              rootMargin: '200px', // Load before the user reaches the end (preload)
              threshold: 0.1 // Trigger when just 10% is visible for quicker loading
            }
          );
          
          observer.observe(infiniteScrollSentinel);
        } else {
          // Fallback for browsers without IntersectionObserver support
          loadMoreContainer.classList.remove('hidden');
        }
      };

      // Fallback/alternative: Load More Button
      loadMoreBtn.addEventListener("click", () => {
        if (hasMoreProducts && !isLoading) {
          fetchProducts(false);
          
          // Add haptic feedback if available
          if ("vibrate" in navigator) navigator.vibrate(30);
        }
      });

      // --- Notifications & Interactions ---
      const showNotification = (message, isError = false) => {
        let notificationContainer = document.querySelector(
          ".notification-container"
        );

        if (!notificationContainer) {
          notificationContainer = document.createElement("div");
          notificationContainer.className =
            "notification-container fixed bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
          document.body.appendChild(notificationContainer);
        }

        const toast = document.createElement("div");
        toast.className =
          "bg-white text-gray-800 px-5 py-3.5 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 border border-gray-100 flex items-center";

        // Choose icon based on message content
        let iconName = "info";
        if (message.includes("added")) iconName = "check_circle";
        else if (message.includes("Applied") || message.includes("applied"))
          iconName = "local_offer";
        else if (message.includes("removed") || isError)
          iconName = "error_outline";
        else if (message.includes("reset") || message.includes("filters"))
          iconName = "filter_list";
        else if (message.includes("sorted")) iconName = "sort";
        else if (message.includes("Found")) iconName = "search";
        else if (message.includes("Showing")) iconName = "category";

        const iconColor = isError ? "text-red-500" : "text-[#5466F7]";

        toast.innerHTML = `
          <span class="material-icons-round ${iconColor} mr-2">${iconName}</span>
          <span>${message}</span>
        `;

        notificationContainer.appendChild(toast);

        // Animation
        setTimeout(() => {
          toast.classList.add("opacity-100");
          toast.classList.remove("translate-y-4");
        }, 10);

        setTimeout(() => {
          toast.classList.remove("opacity-100");
          toast.classList.add("opacity-0", "translate-y-4");
          setTimeout(() => {
            toast.remove();
          }, 300);
        }, 3000);
      };

      const setupSwipeToWishlist = (element, product) => {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;
        let threshold = 80;
        const overlay = element.querySelector(".wishlist-overlay");
        const contentElement = overlay?.querySelector("div");

        if (!overlay || !contentElement) return;

        element.addEventListener("touchstart", (e) => {
          startX = e.touches[0].clientX;
          startY = e.touches[0].clientY;
        });

        element.addEventListener(
          "touchmove",
          (e) => {
            endX = e.touches[0].clientX;
            endY = e.touches[0].clientY;

            // Calculate distance
            const distX = endX - startX;
            const distY = endY - startY;

            // Show overlay based on swipe distance (for visual feedback)
            if (Math.abs(distX) > Math.abs(distY) && distX > 30) {
              const opacity = Math.min(distX / threshold, 0.8);
              overlay.style.opacity = opacity;
              contentElement.style.transform = `translateY(${Math.max(4 - opacity * 8, 0)}px)`;

              // If mostly horizontal swipe
              if (Math.abs(distX) > Math.abs(distY) && Math.abs(distX) > 30) {
                e.preventDefault();
              }
            }
          },
          { passive: false }
        );

        element.addEventListener("touchend", (e) => {
          endX = e.changedTouches[0].clientX;
          endY = e.changedTouches[0].clientY;

          // Calculate distance
          const distX = endX - startX;
          const distY = endY - startY;

          // Reset overlay if swipe wasn't completed
          if (distX <= threshold || Math.abs(distY) >= 50) {
            overlay.style.opacity = 0;
            contentElement.style.transform = "translateY(4px)";
          }

          // If swiped right with enough distance and not too much vertical movement
          if (distX > threshold && Math.abs(distY) < 50) {
            addToWishlist(product, overlay, contentElement);
          }
        });
      };

      const addToWishlist = (product, overlay, contentElement) => {
        // Add haptic feedback if available
        if ("vibrate" in navigator) {
          navigator.vibrate([30, 30, 50]);
        }

        // Show wishlist overlay animation
        overlay.style.opacity = "0.9";
        contentElement.style.transform = "translateY(0)";

        setTimeout(() => {
          overlay.style.opacity = "0";
          contentElement.style.transform = "translateY(4px)";
        }, 1500);

        // Show a notification toast
        showNotification(`${product.name} added to wishlist!`);
      };

      const addToCart = (product) => {
        // Add haptic feedback if available
        if ("vibrate" in navigator) {
          navigator.vibrate(50);
        }

        // Cart animation
        const btn = document.querySelector(
          `[data-id="${product.id}"] .quick-add-btn`
        );

        if (btn) {
          btn.classList.add("scale-110", "bg-green-500");
          btn.innerHTML =
            '<span class="material-icons-round text-lg">check</span>';

          setTimeout(() => {
            btn.classList.remove("scale-110", "bg-green-500");
            btn.innerHTML =
              '<span class="material-icons-round text-lg">add</span>';
          }, 1500);
        }

        // If there's a cart utility, add the product
        if (window.CartUtils) {
          window.CartUtils.addToCart({
            id: product.id,
            name: product.name,
            quantity: 1,
            price: product.price, // Make sure price is included if needed by CartUtils
            image_url: product.image_url, // Make sure image is included if needed
          });
        }

        // Show a notification toast
        showNotification(`${product.name} added to cart!`);
      };

      // --- Component Initialization & Cleanup ---
      
      // Initialize the component
      const initializeComponent = () => {
        // Setup intersection observer for infinite scrolling
        setupInfiniteScroll();
        
        // Update price range slider visual on load
        if (priceRange) {
          const percent = (priceRange.value - priceRange.min) / (priceRange.max - priceRange.min) * 100;
          priceRange.style.backgroundSize = `${percent}% 100%`;
          
          // Update slider fill when value changes
          priceRange.addEventListener('input', function() {
            const percent = (this.value - this.min) / (this.max - this.min) * 100;
            this.style.backgroundSize = `${percent}% 100%`;
          });
        }
        
        // Initial data fetch
        fetchProducts(true);
      };
      
      // Clean up resources when page unloads
      const cleanupResources = () => {
        // Disconnect observer to prevent memory leaks
        if (observer) {
          observer.disconnect();
        }
        
        // Cancel any pending requests
        // This is a basic implementation - in a more complex app, 
        // you'd keep track of all fetch AbortControllers and abort them here
      };
      
      // Register cleanup on page unload
      window.addEventListener('beforeunload', cleanupResources);
      
      // Initialize the component
      initializeComponent();

      // --- Handle Category Slides Interaction ---
      const categorySlides = document.querySelectorAll('.category-slide');

      // Update category slides UI to reflect current selection
      const updateCategorySlideUI = (categoryId) => {
        categorySlides.forEach((slide) => {
          const slideCategory = slide.getAttribute('data-category');
          const imageContainer = slide.querySelector('div');
          const text = slide.querySelector('p');
          
          // Reset all slides to default styling
          imageContainer.classList.remove('border-[#5466F7]', 'shadow-sm');
          text.classList.remove('text-[#5466F7]');
          text.classList.add('text-gray-600');
          
          // Apply active styling to selected category
          if (slideCategory === categoryId) {
            imageContainer.classList.add('border-[#5466F7]', 'shadow-sm');
            text.classList.remove('text-gray-600');
            text.classList.add('text-[#5466F7]');
          }
        });
      };

      // Add click listeners to category slides
      categorySlides.forEach(slide => {
        slide.addEventListener('click', () => {
          const categoryId = slide.getAttribute('data-category');
          if (categoryId === activeFilters.category) return; // Avoid refetching if unchanged
          
          // Add haptic feedback if available
          if ("vibrate" in navigator) navigator.vibrate(40);
          
          // Update category in active filters
          activeFilters.category = categoryId;
          
          // Update UI to show active category
          updateCategorySlideUI(categoryId);
          
          // Fetch products for the selected category
          fetchProducts(true);
          
          // Show notification about category change
          showNotification(`Showing ${categoryId === "all" ? "all products" : slide.querySelector('p').textContent.trim()}`);
        });
      });
    });
  </script>

  <style is:inline>
    /* Improved scrollbar hiding */
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }

    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    /* Smoother transitions for products loading */
    #products-grid {
      transition: opacity 0.4s ease;
    }

    /* Enhanced hover effects for product cards */
    .product-card {
      transform: translateY(0);
      transition:
        transform 0.2s ease,
        box-shadow 0.3s ease,
        opacity 0.3s ease;
    }

    .product-card:hover {
      transform: translateY(-2px);
    }

    .product-card .quick-add-btn {
      opacity: 1;
      transform: translateY(0) scale(1);
      transition: all 0.2s ease;
    }

    /* Enhanced image hover animation */
    .product-card:hover .product-image-container img {
      transform: scale(1.05);
    }

    /* Improved active state for category buttons */
    .data-category-btn.bg-\[\#5466F7\] {
      border-width: 0;
      font-weight: 600;
    }

    /* Enhanced range slider styling */
    input[type="range"] {
      -webkit-appearance: none;
      height: 6px;
      background: #e5e7eb;
      border-radius: 5px;
      background-image: linear-gradient(#5466f7, #5466f7);
      background-size: 100% 100%; /* Updated to cover full range initially */
      background-repeat: no-repeat;
    }

    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      height: 20px;
      width: 20px;
      border-radius: 50%;
      background: #5466f7;
      box-shadow: 0 2px 4px rgba(84, 102, 247, 0.3);
      cursor: pointer;
      border: 2px solid white;
    }

    input[type="range"]::-webkit-slider-runnable-track {
      -webkit-appearance: none;
      box-shadow: none;
      border: none;
      background: transparent;
    }

    /* Enhanced loading animation */
    @keyframes pulse-shadow {
      0%,
      100% {
        box-shadow: 0 0 0 0 rgba(84, 102, 247, 0.4);
      }
      50% {
        box-shadow: 0 0 0 8px rgba(84, 102, 247, 0);
      }
    }

    #loading-indicator div {
      animation:
        spin 0.8s linear infinite,
        pulse-shadow 2s infinite;
    }

    /* Enhanced modal animations */
    #sort-modal .transform,
    #filter-modal .transform {
      border-radius: 24px 24px 0 0;
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
      will-change: transform;
    }

    /* Animation for product filtering */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

   
  </style>
</MainLayout>
